---
import '../styles/global.css';
import { getCollection } from "astro:content";
import BaseLayout from '../layouts/BaseLayout.astro';
import BlogPost from '../components/BlogPost.astro';

const allPosts = await getCollection("posts");
const pageTitle = "Mon blog d'apprentissage Astro";
---
<BaseLayout pageTitle={pageTitle}>
  <p>C'est ici que je vais publier sur mon voyage d'apprentissage d'Astro.</p>
  {
    allPosts.map((post) => (
      <BlogPost url={`/posts/${post.slug}/`} title={post.data.title} />
    ))
  }
</BaseLayout>
  </body>
</html>