import rss from '@astrojs/rss';
import { getCollection } from 'astro:content';

export async function GET(context) {
  const posts = await getCollection("posts");
  return rss({
    title: 'B<PERSON> <PERSON>',
    description: "Mon voyage d'apprentissage d'Astro",
    site: 'https://portfolio-ikigaigp.netlify.app',
    items: await pagesGlobToRssItems(import.meta.glob('./**/*.md')),
    items: posts.map((post) => ({
      title: post.data.title,
      pubDate: post.data.pubDate,
      description: post.data.description,
      link: `/posts/${post.slug}/`,
    })),
    customData: `<language>fr-fr</language>`,
  });
}