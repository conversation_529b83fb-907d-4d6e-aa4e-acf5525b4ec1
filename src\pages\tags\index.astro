---
import { getCollection } from "astro:content";
import BaseLayout from "../../layouts/BaseLayout.astro";
const allPosts = await getCollection("posts");
const tags = [...new Set(allPosts.map((post) => post.data.tags).flat())];
const pageTitle = "Tag Index";
---
<BaseLayout pageTitle={pageTitle}>
  <div class="tags">
    {
      tags.map((tag) => (
        <p class="tag">
          <a href={`/tags/${tag}`}>{tag}</a>
        </p>
      ))
    }
  </div>
</BaseLayout>
<style>
  a {
    color: #00539f;
  }

  .tags {
    display: flex;
    flex-wrap: wrap;
  }

  .tag {
    margin: 0.25em;
    border: dotted 1px #a1a1a1;
    border-radius: 0.5em;
    padding: 0.5em 1em;
    font-size: 1.15em;
    background-color: #f8fcfd;
  }
</style>
