---
import { getCollection } from "astro:content";
import BaseLayout from '../../layouts/BaseLayout.astro';
import BlogPost from '../../components/BlogPost.astro';

export async function getStaticPaths() {
  const allPosts = await getCollection("posts");

  const uniqueTags = [...new Set(allPosts.map((post) =>

 post.data.tags).flat())];

  return uniqueTags.map((tag) => {
    const filteredPosts = allPosts.filter((post) => post.data.tags.includes(tag));
    return {
      params: { tag },
      props: { posts: filteredPosts },
    };
  });
}

const { tag } = Astro.params;
const { posts } = Astro.props;
---
<BaseLayout pageTitle={tag}>
  <p>Articles marqués avec {tag}</p>
  <ul>
    {
      posts.map((post) => (
        <BlogPost url={`/posts/${post.slug}/`} title={post.data.title} />
      ))
    }
  </ul>
</BaseLayout>