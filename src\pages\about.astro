---
import '../styles/global.css';

import BaseLayout from '../layouts/BaseLayout.astro';

const pageTitle = "About Me";

const identity = {
  firstName: "Guillaume",
  country: "France",
  occupation: "web developer",
  hobbies: ["sport", "video games", "new technologies"],
};
const skills = ["HTML", "CSS", "JavaScript", "React", "Astro", "Python"];

const happy = true;
const finished = true;
const goal = 3;

const skillColor = "navy";
const fontWeight = "bold";
const textCase = "uppercase";
---

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width" />
    <meta name="generator" content={Astro.generator} />
    <title>{pageTitle}</title>
  </head>
  <body>

<BaseLayout>
    <h1>{pageTitle}</h1>

    <h2>... and my new Astro site!</h2>

    <p>
      I am working through <PERSON><PERSON>'s introductory tutorial. This is the second
      page on my website, and it's the first one I built myself!
    </p>

    <p>
      This site will update as I complete more of the tutorial, so keep checking
      back and see how my journey is going!
    </p>
    <p>Voici quelques faits me concernant :</p>
    <ul>
      <li>Je m'appelle {identity.firstName}.</li>
      <li>Je vis au {identity.country} et je travaille en tant que {identity.occupation}.</li>
      {identity.hobbies.length >= 2 &&
        <li>Deux de mes loisirs sont : {identity.hobbies[0]} et {identity.hobbies[1]}</li>
      }
    </ul>
    <p>Voici mes compétences :</p>
<ul>
  {skills.map((skill) => <li class="skill">{skill}</li>)}
</ul>
{happy && <p>Je suis heureux d'apprendre Astro !</p>}

{finished && <p>J'ai terminé ce tutoriel !</p>}

{goal === 3 ? <p>Mon objectif est de terminer en 3 jours.</p> : <p>Mon objectif n'est pas de 3 jours.</p>}
</BaseLayout>
  </body>
</html>
