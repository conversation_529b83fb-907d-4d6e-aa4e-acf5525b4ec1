---
import BaseLayout from './BaseLayout.astro';
import { fade } from "astro:transitions";
const { frontmatter } = Astro.props;
---
<BaseLayout pageTitle={frontmatter.title}>
  <p>{frontmatter.pubDate.toString().slice(0,10)}</p>
  <p transition:animate={fade({ duration: '2s' })}><em>{frontmatter.description}</em></p>
  <p>Rédigé par :<a href="/about/">{frontmatter.author}</a></p>

  <img src={frontmatter.image.url} width="300" alt={frontmatter.image.alt} />

  <div class="tags">
    {frontmatter.map((tag) => (
      <p class="tag"><a href={`/tags/${tag}`}>{tag}</a></p>
    ))}
  </div>

  <slot />
</BaseLayout>
<style>
  a {
    color: #00539F;
  }

  .tags {
    display: flex;
    flex-wrap: wrap;
  }

  .tag {
    margin: 0.25em;
    border: dotted 1px #a1a1a1;
    border-radius: .5em;
    padding: .5em 1em;
    font-size: 1.15em;
    background-color: #F8FCFD;
  }
</style>